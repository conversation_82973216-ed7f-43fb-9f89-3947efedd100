\documentclass[12pt]{article}

\usepackage{amsthm}
\usepackage{amsmath,amssymb}
\usepackage{lmodern}
\usepackage[T1]{fontenc}
% \usepackage{microtype}
\usepackage[letterpaper, margin=1in]{geometry}
\usepackage{hyperref}
\usepackage{multirow}
\usepackage{tabularx}
\usepackage{colortbl}
\usepackage{color}
\usepackage{graphicx}
\usepackage[small]{caption}
\usepackage{setspace}

% 中文支持
\usepackage{ctex}

\newtheorem{theorem}{定理}[section]
\newtheorem{lemma}[theorem]{引理}
\newtheorem{fact}[theorem]{事实}
\newtheorem{proposition}[theorem]{命题}
\newtheorem{claim}[theorem]{断言}
\newtheorem{observation}[theorem]{观察}
\newtheorem{corollary}[theorem]{推论}
\newtheorem{remark}[theorem]{注记}
\newtheorem{conjecture}[theorem]{猜想}
\newtheorem{definition}[theorem]{定义}

% 设置paragraph和subparagraph的格式
\setcounter{secnumdepth}{5} % 让paragraph和subparagraph显示编号
\setcounter{tocdepth}{5}    % 在目录中显示paragraph和subparagraph

% 重新定义paragraph格式：显示编号，不缩进，换行
\makeatletter
\renewcommand\paragraph{%
  \@startsection{paragraph}{4}{\z@}%
    {3.25ex \@plus1ex \@minus.2ex}%
    {1.5ex \@plus.2ex}%
    {\normalfont\normalsize\bfseries}}
\makeatother

% 重新定义subparagraph格式：显示编号，不缩进，换行
\makeatletter
\renewcommand\subparagraph{%
  \@startsection{subparagraph}{5}{\z@}%
    {3.25ex \@plus1ex \@minus.2ex}%
    {1.5ex \@plus.2ex}%
    {\normalfont\normalsize\bfseries}}
\makeatother

% Aliases
\newcommand{\llabel}[1]{\label{#1}}
\newcommand{\heading}[1]{{\bf #1}}

\newcommand{\zo}{\{0,1\}}
\newcommand{\mzo}{\{-1,+1\}}
\newcommand{\F}{{\mathbb{F}}}
\newcommand{\N}{{\mathbb{N}}}
\newcommand{\Z}{{\mathbb{Z}}}
\newcommand{\R}{{\mathbb{R}}}
\newcommand{\C}{{\mathbb{C}}}
\newcommand{\eps}{\varepsilon}
\newcommand{\tO}{\tilde{O}}
\newcommand{\Vol}{\mathop\mathrm{Vol}\nolimits}
\newcommand{\Const}{\mathop\mathrm{Const}\nolimits}
\newcommand{\E}[2]{{\mathbb{E}_{#1}\left[#2\right]}}
\newcommand{\EE}[2]{{\mathbb{E}_{#1}{#2}}}
\newcommand{\EX}{{\mathbb E}}
\newcommand{\Sur}{\mathop\mathrm{Sur}\nolimits}
\newcommand{\polylog}{\mathop\mathrm{polylog}\nolimits}
\newcommand{\xor}{\oplus}
\newcommand{\conj}[1]{{\overline {#1}}} %% conjugate
\DeclareMathOperator{\ed}{ed}
\DeclareMathOperator{\inv}{inv}
\DeclareMathOperator{\poly}{poly}
\DeclareMathOperator{\sk}{{\bf sk}}
\DeclareMathOperator{\Supp}{supp}
\DeclareMathOperator{\wt}{wt}
\newcommand{\aset}[1]{\{ #1 \}}
\newcommand{\Hc}{\mathcal{H}}
\newcommand{\Zbb}{\mathbb{Z}}

% 自定义数学运算符
\DeclareMathOperator{\Var}{\mathbf{Var}}
\DeclareMathOperator{\Cov}{\mathbf{Cov}}
\DeclareMathOperator{\Inf}{Inf}
\DeclareMathOperator{\Stab}{Stab}

\title{Locality Sensitive Hashing}


\begin{document}
\maketitle

% \begin{abstract}

% \end{abstract}

\onehalfspacing
\input{introduction}
\input{background}
\input{details}
\input{conclusion}

\singlespacing
{\small
\bibliographystyle{alpha}
\bibliography{main}
}

\end{document}
