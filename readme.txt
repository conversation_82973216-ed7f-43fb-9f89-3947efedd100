这里是一个论文解释的任务。

我们的任务产出是在hw文件夹里的，注意严格遵循hw文件夹里的latex文件的格式。
hw文件夹中你不允许删去任何已有的东西，只能添加新的内容。你添加的地方要用注释标注出来。

我们解读的论文是在Lower bounds on Locality Sensitive Hashing文件夹中，请你仔细阅读并且理解（你只有读取的权限）

另外，论文《Lower bounds on Locality Sensitive Hashing》总结.md这个文件是我们总结的指导任务的文件（你只有读取的权限）。你的任务有且仅有这个文件中的以下部分（原文在这个文件中）：
以**汉明立方体**（\(\{0,1\}^d\)，\(\ell_1\)范数）为核心研究对象，通过“哈希原像集分析+随机游走+傅里叶工具”推导，步骤如下：
1. **原像集大小估计**（引理2.2、推论2.3）：对随机点\(x\in\{0,1\}^d\)，其哈希原像集\(\mathscr{H}^{-1}(\mathscr{H}(x))\)的期望大小受限于\(R\)（距离阈值），当\(R<\frac{d}{2}\)时，期望大小≤\(2^d\left(q + e^{-\frac{1}{d}(\frac{d}{2}-R)^2}\right)\)。

总之，你的任务是，遵循论文《Lower bounds on Locality Sensitive Hashing》总结.md中的是四、证明关键思路中的1. **原像集大小估计**部分的要求，
在hw文件夹中补充。然后把你补充的大致内容总结出来告诉我。
