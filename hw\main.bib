@article{andoni2015tight,
  title={Tight lower bounds for data-dependent locality-sensitive hashing},
  author={<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, Ilya},
  journal={arXiv preprint arXiv:1507.04299},
  year={2015}
}

@book{o2021analysis, place={Cambridge}, title={Analysis of Boolean Functions}, publisher={Cambridge University Press}, author={<PERSON><PERSON>, <PERSON>}, year={2014}}

@article{Motwani2008LowerBO,
author = {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, Assa<PERSON> and <PERSON>igrahy, Rina},
title = {Lower Bounds on Locality Sensitive Hashing},
journal = {SIAM Journal on Discrete Mathematics},
volume = {21},
number = {4},
pages = {930-935},
year = {2008},
doi = {10.1137/050646858},

URL = { 
    
        https://doi.org/10.1137/050646858
    
    

},
eprint = { 
    
        https://doi.org/10.1137/050646858
    
    

}
,
    abstract = { Given a metric space \$(X,d\_X)\$, \$c \ge 1\$, \$r > 0\$, and \$p,q \in [0,1]\$, a distribution over mappings \$\mathscr{H} : X \to \mathbb{N}\$ is called a \$(r,cr,p,q)\$-sensitive hash family if any two points in X at distance at most r are mapped by \$\mathscr{H}\$ to the same value with probability at least p, and any two points at distance greater than \$cr\$ are mapped by \$\mathscr{H}\$ to the same value with probability at most q. This notion was introduced by Indyk and Motwani in 1998 as the basis for an efficient approximate nearest neighbor search algorithm and has since been used extensively for this purpose. The performance of these algorithms is governed by the parameter \$\rho = \frac{\log(1/p)}{\log(1/q)}\$, and constructing hash families with small \$\rho\$ automatically yields improved nearest neighbor algorithms. Here we show that for \$X = \ell\_1\$ it is impossible to achieve \$\rho \le \frac{1}{2c}\$. This almost matches the construction of Indyk and Motwani which achieves \$\rho \le \frac{1}{c}\$. }
}


@inproceedings{andoni2017optimal,
        title={Optimal hashing-based time-space trade-offs for approximate near neighbors},
        author={Andoni, Alexandr and Laarhoven, Thijs and Razenshteyn, Ilya and Waingarten, Erik},
        booktitle={Proceedings of the twenty-eighth annual ACM-SIAM symposium on discrete algorithms},
        pages={47--66},
        year={2017},
        organization={SIAM}
}

@inproceedings{GIM1999,
author = {Gionis, Aristides and Indyk, Piotr and Motwani, Rajeev},
title = {Similarity Search in High Dimensions via Hashing},
year = {1999},
isbn = {1558606157},
publisher = {Morgan Kaufmann Publishers Inc.},
address = {San Francisco, CA, USA},
booktitle = {Proceedings of the 25th International Conference on Very Large Data Bases},
pages = {518–529},
numpages = {12},
series = {VLDB '99}
}

@article{OWZ2014,
author = {O’Donnell, Ryan and Wu, Yi and Zhou, Yuan},
title = {Optimal Lower Bounds for Locality-Sensitive Hashing (Except When q is Tiny)},
year = {2014},
issue_date = {March 2014},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
volume = {6},
number = {1},
issn = {1942-3454},
url = {https://doi.org/10.1145/2578221},
doi = {10.1145/2578221},
abstract = {We study lower bounds for Locality-Sensitive Hashing (LSH) in the strongest setting: point sets in {0,1}d under the Hamming distance. Recall that H is said to be an (r, cr, p, q)-sensitive hash family if all pairs x, y ∈ {0,1}d with dist(x, y) ≤ r have probability at least p of collision under a randomly chosen h ∈ H, whereas all pairs x, y ∈ {0, 1}d with dist(x, y) ≥ cr have probability at most q of collision. Typically, one considers d → ∞, with c > 1 fixed and q bounded away from 0.For its applications to approximate nearest-neighbor search in high dimensions, the quality of an LSH family H is governed by how small its ρ parameter ρ = ln(1/p)/ln(1/q) is as a function of the parameter c. The seminal paper of Indyk and Motwani [1998] showed that for each c ≥ 1, the extremely simple family H = {x ↦ xi : i ∈ [d]} achieves ρ ≤ 1/c. The only known lower bound, due to Motwani et al. [2007], is that ρ must be at least ( e1/c - 1)/(e1/c + 1) ≥ .46/c (minus od(1)). The contribution of this article is twofold. (1) We show the “optimal” lower bound for ρ: it must be at least 1/c (minus od(1)). Our proof is very simple, following almost immediately from the observation that the noise stability of a boolean function at time t is a log-convex function of t. (2) We raise and discuss the following issue: neither the application of LSH to nearest-neighbor search nor the known LSH lower bounds hold as stated if the q parameter is tiny. Here, “tiny” means q = 2-Θ(d), a parameter range we believe is natural.},
journal = {ACM Trans. Comput. Theory},
month = mar,
articleno = {5},
numpages = {13},
keywords = {noise stability, noise sensitivity, locality-sensitive hashing, LSH, Fourier analysis of boolean functions}
}

@INPROCEEDINGS{AP2006,
  author={Andoni, Alexandr and Indyk, Piotr},
  booktitle={2006 47th Annual IEEE Symposium on Foundations of Computer Science (FOCS'06)}, 
  title={Near-Optimal Hashing Algorithms for Approximate Nearest Neighbor in High Dimensions}, 
  year={2006},
  volume={},
  number={},
  pages={459-468},
  keywords={Nearest neighbor searches;Data structures;Image databases;Spatial databases;Approximation algorithms;Probes;Decoding;Lattices;Data compression;Data mining},
  doi={10.1109/FOCS.2006.49}}

@inproceedings{AIRLS2015,
author = {Andoni, Alexandr and Indyk, Piotr and Laarhoven, Thijs and Razenshteyn, Ilya and Schmidt, Ludwig},
title = {Practical and optimal LSH for angular distance},
year = {2015},
publisher = {MIT Press},
address = {Cambridge, MA, USA},
abstract = {We show the existence of a Locality-Sensitive Hashing (LSH) family for the angular distance that yields an approximate Near Neighbor Search algorithm with the asymptotically optimal running time exponent. Unlike earlier algorithms with this property (e.g., Spherical LSH [1, 2]), our algorithm is also practical, improving upon the well-studied hyperplane LSH [3] in practice. We also introduce a multiprobe version of this algorithm and conduct an experimental evaluation on real and synthetic data sets.We complement the above positive results with a fine-grained lower bound for the quality of any LSH family for angular distance. Our lower bound implies that the above LSH family exhibits a trade-off between evaluation time and quality that is close to optimal for a natural class of LSH functions.},
booktitle = {Proceedings of the 29th International Conference on Neural Information Processing Systems - Volume 1},
pages = {1225–1233},
numpages = {9},
location = {Montreal, Canada},
series = {NIPS'15}
}

@inproceedings{AR2015,
author = {Andoni, Alexandr and Razenshteyn, Ilya},
title = {Optimal Data-Dependent Hashing for Approximate Near Neighbors},
year = {2015},
isbn = {9781450335362},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/2746539.2746553},
doi = {10.1145/2746539.2746553},
abstract = {We show an optimal data-dependent hashing scheme for the approximate near neighbor problem. For an n-point dataset in a d-dimensional space our data structure achieves query time O(d ⋅ nρ+o(1)) and space O(n1+ρ+o(1) + d ⋅ n), where ρ=1/(2c2-1) for the Euclidean space and approximation c>1. For the Hamming space, we obtain an exponent of ρ=1/(2c-1). Our result completes the direction set forth in (Andoni, Indyk, Nguyen, Razenshteyn 2014) who gave a proof-of-concept that data-dependent hashing can outperform classic Locality Sensitive Hashing (LSH). In contrast to (Andoni, Indyk, Nguyen, Razenshteyn 2014), the new bound is not only optimal, but in fact improves over the best (optimal) LSH data structures (Indyk, Motwani 1998) (Andoni, Indyk 2006) for all approximation factors c>1.From the technical perspective, we proceed by decomposing an arbitrary dataset into several subsets that are, in a certain sense, pseudo-random.},
booktitle = {Proceedings of the Forty-Seventh Annual ACM Symposium on Theory of Computing},
pages = {793–801},
numpages = {9},
keywords = {data structures, decision trees, high-dimensional geometry, similarity search, theory},
location = {Portland, Oregon, USA},
series = {STOC '15}
}

@inproceedings{KL2021,
author = {Kirshanova, Elena and Laarhoven, Thijs},
title = {Lower Bounds on Lattice Sieving and Information Set Decoding},
year = {2021},
isbn = {978-3-030-84244-4},
publisher = {Springer-Verlag},
address = {Berlin, Heidelberg},
url = {https://doi.org/10.1007/978-3-030-84245-1_27},
doi = {10.1007/978-3-030-84245-1_27},
abstract = {In two of the main areas of post-quantum cryptography, based on lattices and codes, nearest neighbor techniques have been used to speed up state-of-the-art cryptanalytic algorithms, and to obtain the lowest asymptotic cost estimates to date [May–Ozerov, Eurocrypt’15; Becker–Ducas–Gama–Laarhoven, SODA’16]. These upper bounds are useful for assessing the security of cryptosystems against known attacks, but to guarantee long-term security one would like to have closely matching lower bounds, showing that improvements on the algorithmic side will not drastically reduce the security in the future. As existing lower bounds from the nearest neighbor literature do not apply to the nearest neighbor problems appearing in this context, one might wonder whether further speedups to these cryptanalytic algorithms can still be found by only improving the nearest neighbor subroutines.We derive new lower bounds on the costs of solving the nearest neighbor search problems appearing in these cryptanalytic settings. For the Euclidean metric we show that for random data sets on the sphere, the locality-sensitive filtering approach of [Becker–Ducas–Gama–Laarhoven, SODA 2016] using spherical caps is optimal, and hence within a broad class of lattice sieving algorithms covering almost all approaches to date, their asymptotic time complexity of 20.292d+o(d) is optimal. Similar conditional optimality results apply to lattice sieving variants, such as the 20.265d+o(d) complexity for quantum sieving [Laarhoven, PhD thesis 2016] and previously derived complexity estimates for tuple sieving [Herold–Kirshanova–Laarhoven, PKC 2018]. For the Hamming metric we derive new lower bounds for nearest neighbor searching which almost match the best upper bounds from the literature [May–Ozerov, Eurocrypt 2015]. As a consequence we derive conditional lower bounds on decoding attacks, showing that also here one should search for improvements elsewhere to significantly undermine security estimates from the literature.},
booktitle = {Advances in Cryptology - CRYPTO 2021: 41st Annual International Cryptology Conference, CRYPTO 2021, Virtual Event, August 16-20, 2021, Proceedings, Part II},
pages = {791–820},
numpages = {30}
}

@article{johnson1984extensions,
  title={Extensions of Lipschitz mappings into a Hilbert space},
  author={Johnson, William B and Lindenstrauss, Joram and others},
  journal={Contemporary mathematics},
  volume={26},
  number={189-206},
  pages={1},
  year={1984}
}

@article{herold2025sublinear,
  title={Sublinear Data Structures for Nearest Neighbor in Ultra High Dimensions},
  author={Herold, Martin G and Nanongkai, Danupon and Spoerhase, Joachim and Varma, Nithin and Wu, Zihang},
  journal={arXiv preprint arXiv:2503.03079},
  year={2025}
}