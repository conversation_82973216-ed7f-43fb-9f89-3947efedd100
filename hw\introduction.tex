\section{Introduction}

\subsection{这篇论文解决了什么问题}

对于解决 $(r, cr)$-ANN 问题的经典 Locality Sensitive Hashing (LSH) 方法，其时间和空间复杂度均由$\rho = \frac{\ln p}{\ln q}$决定。

本文给出了一个 $\rho$ 的下界，证明在经典的 LSH 算法实现中，仅仅通过改变 Hash 函数，时间和空间复杂度无法进一步降低。

\subsection{这篇论文没有解决什么问题}

\begin{itemize}
\item 这篇论文没有给出$(r, cr)$-ANN问题的下界。
  
  这个问题过于困难，以至于人们仅能够给出过分松弛的下界。想要给出较紧的下界将剑指维数灾难。
  
  目前较好的结果，都来自较多的假设条件。

\item 这篇论文没有给出任何一个时间复杂度和空间复杂度的下界。这篇文章给出的只是，经典 LSH 方法给出的一个时间复杂度上界 $O(n^\rho)$ 和空间复杂度上界 $O(n^{1+\rho})$ 的下界。也就是上界的下界。
\end{itemize}

\subsection{该问题为什么重要}

TODO: ANN 的重要性；下界在算法设计中的重要性。

格密码的安全性需要依赖于 $(r, cr)$-ANN 问题的困难性。也就是说，若 $(r, cr)$-ANN 可以快速解决，那么格密码也可以轻松破解 \cite{KL2021}。

\subsection{相关研究}

\begin{table}[h]
\centering
\begin{tabular}{|c|c|c|c|}
\hline
 & $\{0,1\}^d$ & $\mathbb{R}^d$ & $\mathbb{S}^{d-1}$ \\
\hline
\multirow{2}{*}{Upper Bound} & 
$1/c$ \cite{GIM1999} & $1/c^2$ \cite{AP2006} & $(4-c^2r^2)/(4c^2-c^2r^2)$ \\
& $1/(2c-1)$\footnote{Data Dependent，归约到$\mathbb{R}^d$得来} & $1/(2c^2-1)$ \cite{AR2015}\footnote{Data Dependent} & \cite{AIRLS2015} \\
\hline
\multirow{3}{*}{Lower Bound} & 
$1/c$ \cite{OWZ2014} & $1/c^2$ & $(4-c^2r^2)/(4c^2-c^2r^2)$\footnote{没有显式证明，但能够证明上面的上界是最优的} \\
& $\frac{e-1}{e+1} \cdot \frac{1}{c}$ \cite{Motwani2008LowerBO}\footnote{本文的结论，但已经不够紧} & $1/(2c^2-1)$\footnote{由$\{0,1\}^d$归约得来} & \cite{AIRLS2015} \\
& $1/(2c-1)$ \cite{andoni2015tight}\footnote{Data Dependent and Data Independent} & & \\
\hline
\end{tabular}
\caption{不同空间中LSH算法的上界和下界比较}
\end{table}

\subsubsection{关于上界}

早期的LSH方法是数据无关的，即要求给出的算法在任意分布的输入数据上都以高概率返回正确的结果。

最早是由 \cite{GIM1999} 给出的 Hamming 空间上的一个算法。 TODO: 具体描述

随后 \cite{AP2006} 给出了 Euclidean 空间上的一个算法。这一方法以 p-Stable Distribution 为基础，即多维正态分布与单位向量的内积服从正态分布。另外，这一算法采用 Leech Lattice 的方式，对经过 p-Stable Distribution 投影得到的空间进行划分。

关于球面，实际上球面可以直接用欧几里得空间的LSH方法来解决。但是\cite{AIRLS2015}给出了一种基于Polytope的方法，使得其不仅在理论上是最优的，而且在实践中，Hash函数求解的速率更快。

而在数据相关的LSH方法中，\cite{AR2015}给出了一个利用球面构造的LSH方法，其可以只对预先知道的某个数据集有效，而无需对所有数据集都有效。这就跳出了原有的框架，也打破了下界。

\subsubsection{关于下界}

早期的下界是\cite{Motwani2008LowerBO}给出的，适用于Hamming空间。同时，也可以简单地将其归约到Euclidean空间。但是，这篇文章中的测度集中分析不够精巧，导致其下界过于松弛。

随后\cite{OWZ2014}给出了一个更紧的下界，与\cite{GIM1999}的上界相匹配。但是，其假设条件比较强，因此会被\cite{AR2015}给出的数据相关的方法打破，当然数据相关已经不在经典的 LSH 的框架下了。

在数据相关的方法产生后，\cite{andoni2015tight}给出了新框架下的下界，这个下界是紧的。在这篇文章中，作者还对\cite{Motwani2008LowerBO}的下界进行了改进，得到了数据无关适用的下界，也是$1/(2c-1)$。

除了上述关于$\rho$的一系列下界，\cite{andoni2017optimal}提出了List of Points Model，这个模型刻画了现有LSH的框架。在这个模型中，在空间上我们给出若干$A_i \subseteq \{0,1\}^d$（$i \subseteq [m]$），代表对空间的划分；对于每个查询点$q$，我们定义$I(q) \subseteq [m]$，表示查询点对应的桶。在此模型下，查询时间为$|I(q)| + \sum_{i \in I(q)} |L_i|$而空间复杂度为$m + \sum_{i \subseteq [m]} |L_i|$。这篇文章给出了List of Points Model下，时间复杂度下界为$O(n^{\rho_q})$而空间复杂度下界为$O(n^{1+\rho_u})$，并给出了下界$c\sqrt{\rho_q} + (c-1)\sqrt{\rho_u} \geq \sqrt{2c-1}$，很好地揭示了时间复杂度和空间复杂度之间的关系。同时，这也是少数的直接证明时间和空间复杂度下界的文章。更普适地，该文章还给出了1-Cell Probe和2-Cell Probe模型下的时间和空间复杂度下界，两个模型分别表示一次可以访问1个和2个内存单元。尽管还依赖底层度量空间的鲁棒扩张性质，但给出更普适的紧的下界还是很有意义的。

\subsubsection{关于维数}

以上的下界多数依赖$d \gg \log n$，即空间维度远大于数据集大小的对数。这是在非常高的维数条件下的。同时，在此种情况下，复杂度仅仅与$n$有关而和$d$无关是很好解释的，因为借助Johnson-Lindenstrauss Lemma \cite{johnson1984extensions}，我们可以快速地，以不超过$1 + 1/(\log \log n)^{\Omega(1)}$的误差将$d$维空间中的数据集嵌入到$\Theta(\log n \log \log n)$维空间中，从而消除掉$d$。

但在$d = \Theta(\log n)$的空间中，下界的形式会变得异常复杂。\cite{KL2021}给出了球面上$d = \Theta(\log n)$的下界，这里的下界是依赖$d/\log n$的。

在$d \ll \log n$的情况下，基于LSH的下界意义不再具有很大的意义，$d$也会代替$n$成为复杂度的主要因素。

除此之外，在维数$d \gg n$的情况下（俗称超高维），即使进行Johnson-Lindenstrauss Lemma的嵌入，复杂度仍然会依赖于$d$。同时，LSH函数的求解也会变得异常困难。在这种情况下，基于LSH的方案也存在缺陷。\cite{herold2025sublinear}给出了一个在超高维空间中，基于特征选择的方案。在这个方案中，作者给出了一个基于特征选择的方案，即用预处理的时间来交换查询的时间，在数据库上选择有效的特征进行查询，保证对输入数据以高概率仅考虑选中的坐标就可以得到正确的结果。并且这个方案是加性的近似，即$1+\varepsilon$的近似比。其时间复杂度为$O(n\log(d)/\text{poly}(\varepsilon))$，空间复杂度为$O(n\log(d)/\text{poly}(\varepsilon))$。

\begin{table}[h]
\centering
\begin{tabular}{|c|c|c|}
\hline
维度类别 & 规模关系 & 方法 \\
\hline
Ultra High Dimension & $d = \omega(n)$ & 特征选择 \\
High Dimension & $d = O(\log n)$ & LSH \\
Low Dimension & $d = O(1)$或$d = o(\log n)$ & Voronoi Diagram等 \\
\hline
\end{tabular}
\caption{不同维度下的近似最近邻查询方法}
\end{table}
