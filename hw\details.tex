\section{Details}

\subsection{随机游走引理详解}

随机游走引理是海明空间中，证明 $\rho$ 下界的关键。其表明，任意给一种空间的划分方式，经过若干步的随机游走后，终点与起点落在同一个区域的概率，随步数以指数级别下降。

\subsubsection{前置定义}

首先，定义一些符号和概念便于后续使用。

\begin{description}
\item[$B$:] $\emptyset \neq B \subseteq \{0, 1\}^d$，为 $d$ 维的海明空间中的一个非空子集，表示一个划分，即空间被分为了 $B$ 和 $B^c$ 两个部分，这（暂时还不严格地）与哈希函数的值等同（考虑函数 $\mathbf{1}(x \in B)$）。
\item[$|B|$:] $B$ 的大小，即 $B$ 中元素的个数。
\item[$\mathbf{W}_r(\mathbf{u})$:] 从 $\mathbf{u} \in \{0, 1\}^d$ 出发，经过 $r$ 步的随机游走后的终点\footnote{为了可读性，采用了 \cite{andoni2015tight} 中的记号}。显然，$W_{r,u}$ 是一个随机变量。

随机游走为，重复 $r$ 次如下操作：
\begin{itemize}
\item 从当前点 $\mathbf{u}$ 出发
\item 均匀随机选择一个维度 $i \in [d]$，其中 $d$ 为空间的维度
\item 取反 $\mathbf{u}$ 的第 $i$ 维，即 $\mathbf{u}_i \leftarrow 1 - \mathbf{u}_i$
\item 得到新的点 $\mathbf{u}$，作为下一步的起点
\end{itemize}
\end{description}

\subsubsection{引理陈述}

\begin{theorem}[随机游走引理]\label{thm:random-walk}
\[
\Pr[\mathbf{W}_r(\mathbf{u}) \in B \mid \mathbf{u} \in B] \leq \left(\frac{|B|}{2^d}\right)^{\frac{e^{2r/d} - 1}{e^{2r/d} + 1}}
\]
\end{theorem}

可以发现，定理~\ref{thm:random-walk}几乎构成了我们关心的下界。

\subsubsection{证明概要}

证明过程比较复杂，涉及比较多的布尔函数傅立叶分析。我们将从基础出发，逐步构建出证明。

关于布尔函数傅立叶分析的基础知识，主要参考了\cite{o2021analysis}。

\paragraph{前置知识}

\subparagraph{布尔函数}
不做特殊说明，我们考虑的函数均为 $f: \{0, 1\}^d \to \R$。

例如，$f(x) = \mathbf{1}(x \in B) = \begin{cases}1 & \text{if } \, x \in B, \\ 0 & \text{otherwise}\end{cases}$ 就是一个布尔函数。

同样，我们的划分中的 $h \in \mathcal{H} : \{0, 1\}^d \to \N$ 也是一个布尔函数。

\subparagraph{布尔函数的向量视角}

考虑布尔函数 $f: \{0, 1\}^d \to \R$ 的真值表，假如为输入随意定义一个顺序，其输出就能够很自然的成为一个列向量。这是布尔函数与一般的函数有差别的地方。

\subparagraph{布尔函数的多项式视角}
我们日常见到的 $\R\to\R$ 的函数千奇百怪，但布尔函数由于定义域的大小只有 $2^d$，我们相信任何布尔函数都可以用多项式来表示。

实际上，我们可以定义多项式来表示 $\mathbf{x} \mapsto \mathbf{1}(\mathbf{x} = \mathbf{a})$，为

\[
\text{id}_\mathbf{a}(\mathbf{x}) = \prod_{i \in [d]} \frac{(2 x_i - 1)(2 a_i - 1) + 1}{2}
\]

对其中任一维度，只要代入不难验证，当 $x_i = a_i$ 时， $\frac{(2 x_i - 1)(2 a_i - 1) + 1}{2}$ 这一项为 $1$，否则为 $0$。最后求乘积，就保证每一维都相等才为 $1$。因此，这个多项式确实表示了 $\mathbf{x} = \mathbf{a}$。

考虑把布尔函数真值表写成多项式，对表中一行中的输入 $\mathbf{a}_i$，使用 $\text{id}_{\mathbf{a_i}}(x)$ 乘上期望中的输出 $y$，最后对所有行求和，得到的多项式就可以表示任意的布尔函数：

\[
f(\mathbf{x}) = \sum_{\mathbf{a} \in \{0, 1\}^d} y_{\mathbf{a}} \text{id}_\mathbf{a}(\mathbf{x}).
\]

当然，我们不会直接使用这样的多项式。

\subparagraph{$\chi$ 函数}
注意到我们上面用到了 $2 x_i - 1$，这实际上是一个映射，将 $\{0, 1\}$ 映射到 $\{-1, 1\}$，但实际上在傅立叶分析中选择的不是这个映射。

我们定义 $\chi$ 函数为 $\chi(x) : \{0, 1\} \to \{-1, 1\}$

\[
\chi(x) = (-1)^{x} = \begin{cases}1 & \text{if } \, x = 0, \\ -1 & \text{if } \, x = 1.\end{cases}
\]

这样一来，我们就把 $\{0, 1\}$ 映射到了 $\{-1, 1\}$，而这一操作最主要的目的在于，让 $\mathbb{E}_{x \sim \{0, 1\}}[\chi(x)] = 0$，这会在未来的证明中带来大量的便利。

接下来，我们把 $\chi$ 函数推广到 $d$ 维空间上\footnote{这一步骤主要是为了定义更加自然而添加，实际上，有意义的只有下面的 $\chi_S$}

\[
\chi(\mathbf{x}) = \prod_{i \in [d]} \chi(x_i) = \prod_{i \in [d]} (-1)^{x_i} = (-1)^{\sum_{i \in [d]} x_i}.
\]

最后，还记得我们的目标是多项式中的项，所以我们定义 $\chi_S$ 为

\[
\chi_S(\mathbf{x}) = \prod_{i \in S} \chi(x_i) = \prod_{i \in S} (-1)^{x_i} = (-1)^{\sum_{i \in S} x_i}.
\]

这一函数，也被称为沃尔什函数（Walsh function），即\cite{Motwani2008LowerBO}中的 $W_s(u)$，但我们使用了 $\chi_S$ 以免符号混淆。

\subparagraph{布尔函数的傅立叶变换}

对任意的布尔函数 $f$，我们定义：

\[
f(\mathbf{x}) = \sum_{S \subseteq [d]} \hat{f}(S) \chi_S(\mathbf{x}),
\]

其中 $\hat{f} : 2^{[d]} \to \mathbb{R}$。其中的 $\chi_S$ 就代表多项式中的每一项，而 $\hat{f}(S)$ 则代表了多项式中对应的系数。

关于 $\hat{f}(S)$ 是什么，我们暂时还不知道。不过利用上面最朴素的方法，即按真值表列出再化简的方法，我们一定可以求出这样的系数。因此，上面的定义是合理的。

\subparagraph{布尔函数的内积}

为了求解 $\hat{f}(S)$，我们需要引入一个工具，即布尔函数的内积。

对于笔者这样不熟悉泛函分析的人来说，这个定义比较扭曲的地方在于，内积是定义在\textbf{函数}上的，而不是定义在\textbf{值}上\footnote{这也是傅立叶分析作为泛函分析的一个体现}。不过，因为布尔函数可以写成向量，不能验证，函数的内积正比于函数对应向量的内积。

我们定义布尔函数的内积为

\[
\langle f, g \rangle = 2^{-d} \sum_{\mathbf{x} \in \{0, 1\}^d} f(\mathbf{x}) g(\mathbf{x}) = \mathbb{E}_{\mathbf{x} \sim \{0, 1\}^d} [f(\mathbf{x}) g(\mathbf{x})].
\]

值得注意的是第二个等号，尽管布尔函数的内积是没有随机性的，但我们仍然可以构造出从空间中均匀随机抽取一个点的随机事件，以及相应随机变量 -- $f$ 和 $g$ 的乘积的期望值。也就是说，我们逆用期望的定义，把枚举求和转化为期望。

类似于向量，我们定义范数为

\[
\|f\|_2 = \sqrt{\langle f, f \rangle}
\]

以及

\[
\|f\|_p = \mathbb{E}_{\mathbf{x} \sim \{0, 1\}^d} [|f(\mathbf{x})|^p]^{1/p}.
\]

值得注意的是，$\|f\|_p$ 采用了期望的形式，这样可以应对 $p$ 不是整数的情况。

\subparagraph{布尔函数下的正交基}

我们从下面的引理出发：

\begin{lemma}[Walsh函数的期望性质]\label{lem:walsh-expectation}
对于任意非空集合 $S \subseteq [d]$，我们有
\[
\mathbb{E}[\chi_S(\mathbf{x})] = \mathbb{E}\left[\prod_{i \in S} \chi(x_i)\right] = \begin{cases}1 & \text{if } \, S = \emptyset, \\ 0 & \text{if } \, S \neq \emptyset.\end{cases}
\]
\end{lemma}

\begin{proof}
证明相当直接，利用独立性，我们有
\[
\mathbb{E}\left[\prod_{i \in S} \chi(x_i)\right] = \prod_{i \in S} \mathbb{E}[\chi(x_i)] = \prod_{i \in S} 0 = 0.
\]
而在 $S = \emptyset$ 的情况下，$\chi_S(\mathbf{x}) = 1$，因此期望为 $1$。
\end{proof}

基于引理~\ref{lem:walsh-expectation}，我们可以证明下面的重要定理：

\begin{theorem}[Walsh函数的正交性]\label{thm:walsh-orthogonality}
对于函数 $\chi_S : \{0, 1\}^d \to \{-1, 1\}$ 和 $\chi_T : \{0, 1\}^d \to \{-1, 1\}$，我们有
\[
\langle \chi_S, \chi_T \rangle = \begin{cases}1 & \text{if } \, S = T, \\ 0 & \text{if } \, S \neq T.\end{cases}
\]
\end{theorem}

\begin{proof}
下面我们证明该定理：

\begin{align}
\langle \chi_S, \chi_T \rangle &= 2^{-d} \sum_{\mathbf{x} \in \{0, 1\}^d} \chi_S(\mathbf{x}) \chi_T(\mathbf{x}) \\
&= 2^{-d} \sum_{\mathbf{x} \in \{0, 1\}^d} \prod_{i \in S} \chi(x_i) \prod_{j \in T} \chi(x_j) \\
&= 2^{-d} \sum_{\mathbf{x} \in \{0, 1\}^d} \prod_{i \in (S \cap T)} \chi(x_i)^2 \prod_{i \in (S \cup T) \setminus (S \cap T)} \chi(x_i) \\
&= 2^{-d} \sum_{\mathbf{x} \in \{0, 1\}^d} \prod_{i \in (S \cup T) \setminus (S \cap T)} \chi(x_i) \\
&= \mathbb{E}\left[\prod_{i \in (S \cup T) \setminus (S \cap T)} \chi(x_i)\right]
\end{align}

所以，当 $S = T$ 时，$(S \cup T) \setminus (S \cap T) = \emptyset$，因此期望为 $1$；当 $S \neq T$ 时，$(S \cup T) \setminus (S \cap T) \neq \emptyset$，因此根据引理~\ref{lem:walsh-expectation}，期望为 $0$。
\end{proof}

可以发现，定理~\ref{thm:walsh-orthogonality}表明在布尔函数的傅立叶分析中，任何不同的 $\chi_S$ 和 $\chi_T$ 都是正交的，这使得我们可以将布尔函数的傅立叶变换看作是一个正交基的展开。

\subparagraph{布尔函数的傅立叶变换系数}

根据正交基的性质，我们可以得到布尔函数的傅立叶变换系数 $\hat{f}(S)$ 的计算公式：

\[
\hat{f}(S) = \langle f, \chi_S \rangle = \mathbb{E}[\chi_S(\mathbf{x}) f(\mathbf{x})] = 2^{-d} \sum_{\mathbf{x} \in \{0, 1\}^d} \chi_S(\mathbf{x}) f(\mathbf{x}).
\]

可以简单验证，先利用某一傅立叶变换系数 $\hat{f}(S)$ 展开 $f$，再与 $\chi_S$ 取内积，

\[
\langle f, \chi_S \rangle = \left\langle \sum_{T \subseteq [d]} \hat{f}(T) \chi_T, \chi_S \right\rangle
\]

根据内积的线性性（由于期望的线性性，无需特别证明），我们有

\[
\langle f, \chi_S \rangle = \sum_{T \subseteq [d]} \hat{f}(T) \langle \chi_T, \chi_S \rangle
\]

又由于在 $S = T$ 时，$\langle \chi_T, \chi_S \rangle = 1$，在 $S \neq T$ 时，$\langle \chi_T, \chi_S \rangle = 0$。

因此，$\langle f, \chi_S \rangle$ 就是 $S = T$ 时的 $\hat{f}(S)$，也就是我们所需要的系数 $\hat{f}(S)$。

\subparagraph{布尔函数的傅立叶变换系数的基础性质}

\begin{theorem}[普朗歇尔定理（Plancherel's Theorem）]\label{thm:plancherel}
\footnote{这一定理在\cite{Motwani2008LowerBO}中被称为 Parseval's Identity，但这一名称易与 Parseval's Theorem 混淆}
对于任意两个布尔函数 $f, g: \{0, 1\}^d \to \mathbb{R}$，我们有
\[
\langle f, g \rangle = \mathbb{E}_{\mathbf{x} \sim \{0, 1\}^d} [f(\mathbf{x}) g(\mathbf{x})] = \sum_{S \subseteq [d]} \hat{f}(S) \hat{g}(S).
\]
\end{theorem}

\begin{proof}
同样，利用正交基的性质，我们简单验证

\begin{align}
\langle f, g \rangle &= \left\langle \sum_{S \subseteq [d]} \hat{f}(S) \chi_S, \sum_{T \subseteq [d]} \hat{g}(T) \chi_T \right\rangle \\
&= \sum_{S \subseteq [d]} \hat{f}(S) \left\langle \chi_S, \sum_{T \subseteq [d]} \hat{g}(T) \chi_T \right\rangle \\
&= \sum_{S, T \subseteq [d]} \hat{f}(S) \hat{g}(T) \langle \chi_S, \chi_T \rangle \\
&= \sum_{S \subseteq [d]} \hat{f}(S) \hat{g}(S) \langle \chi_S, \chi_S \rangle
\end{align}
其中最后一步利用了定理~\ref{thm:walsh-orthogonality}的正交性质。
\end{proof}

\subparagraph{噪声与稳定性}

我们经常关心，函数的稳定性，即输入收到扰动后，输出的变化幅度。

首先，我们考虑其中一个维度的扰动对布尔函数的扰动，即将 $\mathbf{x}_i$ 改为 $1 - \mathbf{x}_i$ 带来的变化，我们记作第 $i$ 维的\textbf{影响力}(Influence): $\text{Inf}_i(f) = \Pr_{\mathbf{x} \sim \{0, 1\}^d} [f(\mathbf{x}) \neq f(\mathbf{x}^{\mathbf{x}_i \leftarrow 1 - \mathbf{x}_i})]$。我们可以用导数来刻画影响力：$D_i f(x) = (f(\mathbf{x}^{i \mapsto 1}) - f(x^{i \mapsto 0}))/2$。从上面的定义，我们可以发现蛛丝马迹，对一维上比特的反转正是 Random Walk 中一步的动作。

那么对于不止一步呢？我们又定义了\textbf{噪声稳定性}来刻画这一特征。这一特征就能很好刻画若干步 Random Walk 后的函数变化。

要谈噪声稳定性，我们需要先定义噪声模型。我们使用伯努利噪声模型，即对每个维度 $i$，以概率 $1 - \rho$ 将 $\mathbf{x}_i$ 改为 $1 - \mathbf{x}_i$。

\begin{align}
N_\rho(\mathbf{x}) &= \begin{cases}
x_i & \text{with probability} \, \rho, \\
\text{uniformly random} & \text{with probability} \, 1 - \rho
\end{cases} \\
&= \begin{cases}
\mathbf{x}_i & \text{with probability} \, 1/2 + 1/2 \rho, \\
1 - \mathbf{x}_i & \text{with probability} \, 1/2 - 1/2 \rho
\end{cases}
\end{align}

实际上，这与随机游走基本上是一致的。

在这一模型下，我们定义噪声稳定性为

\[
\text{Stab}_\rho(f) = \mathbb{E}_{\mathbf{x} \sim \{0, 1\}^d, y \sim N_\rho(\mathbf{x})} [f(\mathbf{x}) f(y)]
\]

这实际上刻画了 $\Pr_{\mathbf{x} \sim \{0, 1\}^d, y \sim N_\rho(\mathbf{x})} [f(\mathbf{x}) = f(y)]$，即在噪声模型下，布尔函数的稳定性。

现在，定义噪声算符：

\[
T_\rho f = \mathbb{E}_{y \sim N_\rho(\mathbf{x})} [f(y)]
\]

不难验证，有 $\text{Stab}_\rho(f) = \langle T_\rho f, f \rangle$。

此外，对 $T_\rho f$ 进行傅立叶变换，我们有

\begin{align}
T_\rho f &= T_\rho \sum_{S \subseteq [d]} \hat{f}(S) \chi_S \\
&= \sum_{S \subseteq [d]} \hat{f}(S) T_\rho \chi_S \\
&= \sum_{S \subseteq [d]} \hat{f}(S) (1 - \rho)^{|S|} \chi_S,
\end{align}

因为

\begin{align}
T_\rho \chi_S(\mathbf{x}) &= \mathbb{E}_{y \sim N_\rho(\mathbf{x})} [\chi_S(y)] \\
&= \mathbb{E}_{y \sim N_\rho(\mathbf{x})} \left[\prod_{i \in S} \chi(y_i)\right] \\
&= \prod_{i \in S} \mathbb{E}_{y_i \sim N_\rho(\mathbf{x}_i)} [\chi(y_i)],
\end{align}

到这一步为止，我们仅仅用到了基础的独立性，下面，我们将考虑 $y_i = N_\rho(\mathbf{x}_i)$ 这一性质，得到

\begin{align}
&\prod_{i \in S} \mathbb{E}_{y_i \sim N_\rho(\mathbf{x}_i)} [\chi(y_i)] \\
&= \prod_{i \in S} (1/2 + 1/2 \rho) \chi(x_i) - (1/2 - 1/2 \rho) \chi(x_i) \\
&= \prod_{i \in S} \rho \chi(x_i) \\
&= \rho^{|S|} \prod_{i \in S} \chi(x_i)
\end{align}

代入，得到

\[
T_\rho f = \sum_{S \subseteq [d]} \hat{f}(S) \rho^{|S|} \chi_S. \footnote{这一等式在\cite{Motwani2008LowerBO}中直接作为噪声算符的定义，尽管是等价的，但我们认为这样逻辑更清晰。}
\]

尽管这一段定义走马观花，但我们可以发现，噪声稳定性在一定程度上刻画了随机游走的性质。

\subparagraph{超收缩性定理}

\begin{theorem}[$(p, 2)$-超收缩性定理（Bonami-Beckner不等式） \footnote{这一定理在 \cite{Motwani2008LowerBO} 中被称为 Bonami-Beckner Inequality，而在 \cite{o2021analysis} 中被称为 $(2,q)$-Hypercontractivity Theorem。}]\label{thm:hypercontractivity}
\[
\|T_\rho f\|_2 \leq \|f\|_{1 + \rho^2}
\]
\end{theorem}

从直观上理解，噪声算符 $T_\rho$ 使得布尔函数在扰动之后，变得更加平滑，高阶矩上的特征不再明显。

由于其证明极度复杂，这里仅仅给出一个 $(2, 4)$-超收缩性定理的完整证明，以及对应的 $(4/3, 2)$-超收缩性定理的证明。

\begin{theorem}[$(2, 4)$-超收缩性定理]\label{thm:24-hypercontractivity}
对于任意布尔函数 $f: \{0, 1\}^d \to \mathbb{R}$，我们有
\[
\|T_{1/\sqrt{3}} f\|_4 \leq \|f\|_2.
\]
\end{theorem}

\begin{proof}

此处，我们记 $\mathbf{f}$ 为 $f(\mathbf{x})$，同时记

\[
\mathbf{f} = \mathbf{x}_n \mathbf{d} + \mathbf{e}
\]

其中 $\mathbf{x}_n$ 为 $\mathbf{x}$ 的第 $n$ 个维度，$\mathbf{d}$ 为 $\mathbf{x}$ 对 $\mathbf{x}_n$ 的导数，$\mathbf{e}$ 为余项。$\mathbf{d}$ 和 $\mathbf{e}$ 都是最高为 $d - 1$ 的多项式。

对维数 $d$ 使用数学归纳法，首先在 $d = 0$ 的情况下，$f$ 是一个常数函数，$T_\rho f$ 也是一个常数函数，且 $f = T_\rho f$，因此不等式成立。

我们将 $T_{1/\sqrt{3}}$ 简记为 $T$。

假设在 $d < k$ 的情况下成立，我们证明在 $d = k$ 的情况下也成立。

首先，我们有

\begin{align}
T \mathbf{f} &= T (\mathbf{x}_n \mathbf{d} + \mathbf{e}) \\
&= T \mathbf{x}_n T \mathbf{d} + T \mathbf{e} \\
&= \frac{1}{\sqrt{3}} \mathbf{x}_n T \mathbf{d} + T \mathbf{e}.
\end{align}

那么将其代入 $\mathbb{E}[(T \mathbf{f})^4]$，得到

\begin{align}
\mathbb{E}[(T \mathbf{f})^4] &= \mathbb{E}\left[\left(\frac{1}{\sqrt{3}} \mathbf{x}_n T \mathbf{d} + T \mathbf{e}\right)^4\right] \\
&= \mathbb{E}\left[\left(\frac{1}{\sqrt{3}}\right)^4 \mathbf{x}_n^4 (T \mathbf{d})^4 + 4 \left(\frac{1}{\sqrt{3}}\right)^3 \mathbf{x}_n^3 (T \mathbf{d})^3 T \mathbf{e} \right.\\
&\quad + 6 \left(\frac{1}{\sqrt{3}}\right)^2 \mathbf{x}_n^2 (T \mathbf{d})^2 (T \mathbf{e})^2 \\
&\quad + \left. 4 \frac{1}{\sqrt{3}} T \mathbf{d} (T \mathbf{e})^3 + (T \mathbf{e})^4\right]
\end{align}

由于 $\mathbf{d}, \mathbf{e}$ 与 $\mathbf{x}_n$ 独立，因此我们可以将期望拆分化简，得到

\begin{align}
\mathbb{E}[(T \mathbf{f})^4] &= \left(\frac{1}{3}\right)^2 \mathbb{E}[\mathbf{x}_n^4] \mathbb{E}[(T \mathbf{d})^4] + 4 \left(\frac{1}{3}\right)^{3/2} \mathbb{E}[\mathbf{x}_n^3] \mathbb{E}[(T \mathbf{d})^3] \mathbb{E}[T \mathbf{e}] \\
&\quad + 6 \frac{1}{3} \mathbb{E}[\mathbf{x}_n^2] \mathbb{E}[(T \mathbf{d})^2] \mathbb{E}[(T \mathbf{e})^2] + 4 \frac{1}{\sqrt{3}} \mathbb{E}[T \mathbf{d}] \mathbb{E}[(T \mathbf{e})^3] + \mathbb{E}[(T \mathbf{e})^4] \\
&= \frac{1}{9} \mathbb{E}[(T \mathbf{d})^4] + 2 \mathbb{E}[(T \mathbf{d})^2] \mathbb{E}[(T \mathbf{e})^2] + \mathbb{E}[(T \mathbf{e})^4] \\
&\leq \mathbb{E}[(T \mathbf{d})^4] + 2 \mathbb{E}[(T \mathbf{d})^2] \mathbb{E}[(T \mathbf{e})^2] + \mathbb{E}[(T \mathbf{e})^4]
\end{align}

使用柯西-施瓦茨不等式，我们有

\begin{align}
\mathbb{E}[(T \mathbf{f})^4] &\leq \mathbb{E}[(T \mathbf{d})^4] + 2 \mathbb{E}[(T \mathbf{d})^2] \mathbb{E}[(T \mathbf{e})^2] + \mathbb{E}[(T \mathbf{e})^4] \\
&\leq \mathbb{E}[(T \mathbf{d})^4] + 2 \sqrt{\mathbb{E}[(T \mathbf{d})^4]} \sqrt{\mathbb{E}[(T \mathbf{e})^4]} + \mathbb{E}[(T \mathbf{e})^4]
\end{align}

开始代入归纳假设，得到

\begin{align}
\mathbb{E}[(T \mathbf{f})^4] &\leq \mathbb{E}[(\mathbf{d}^2)]^2 + 2 \sqrt{\mathbb{E}[(\mathbf{d}^2)]^2} \sqrt{\mathbb{E}[(\mathbf{e}^2)]^2} + \mathbb{E}[(\mathbf{e}^2)]^2 \\
&= (\mathbb{E}[(\mathbf{d}^2)] + \mathbb{E}[(\mathbf{e}^2)])^2 \\
&= \mathbb{E}[(\mathbf{d} + \mathbf{e})^2]^2 \\
&= \mathbb{E}[\mathbf{f}^2]^2.
\end{align}
\end{proof}

在开始证明 $(4/3, 2)$-超收缩性定理之前，我们需要先引入一个工具，Hölder不等式。

\begin{lemma}[Hölder不等式]
\label{lem:holder}
对于任意的 $p, q > 0$，满足 $\frac{1}{p} + \frac{1}{q} = 1$，以及任意的可测函数 $f, g$，我们有
\[
\|fg\|_1 \leq \|f\|_p \|g\|_q.
\]
\end{lemma}

证明略，类似 Cauchy-Schwarz 不等式。

\begin{theorem}[$(4/3, 2)$-超收缩性定理]\label{thm:43-hypercontractivity}
对于任意布尔函数 $f: \{0, 1\}^d \to \mathbb{R}$，我们有
\[
\|T_{1/\sqrt{3}} f\|_2 \leq \|f\|_{4/3}.
\]
\end{theorem}


\begin{proof}

\begin{align}
\|T_{1/\sqrt{3}} f\|_2^2 &= \langle T_{1/\sqrt{3}} f, T_{1/\sqrt{3}} f \rangle \\
&= \langle f, T_{1/\sqrt{3}}^2 f \rangle \\
&\leq \|f\|_{4/3} \|T_{1/\sqrt{3}}^2 f\|_4
\end{align}

这一步的理由是 Hölder不等式（引理~\ref{lem:holder}），其中 $p = 4/3$，$q = 4$。

接下来利用 $(2, 4)$-超收缩性定理，得到

\[
\|T_{1/\sqrt{3}}^2 f\|_4 \leq \|T_{1/\sqrt{3}} f\|_2.
\]

因此，对 $\|T_{1/\sqrt{3}} f\|_2^2 \leq \|f\|_{4/3} \|T_{1/\sqrt{3}} f\|_2$ 同除 $\|T_{1/\sqrt{3}} f\|_2$，得到

\[
\|T_{1/\sqrt{3}} f\|_2 \leq \|f\|_{4/3}.
\]
\end{proof}

\subsection{随机游走引理的证明}

经过漫长的铺垫，我们终于可以证明随机游走引理。

类似于马尔可夫随机过程，我们定义矩阵 $P \in \mathbb{R}^{2^{[d]} \times 2^{[d]}}$ 也即每个行、列可以代表一个 $\mathbf{x} \in \{0, 1\}^d$，其中 $P_{\mathbf{x}, \mathbf{y}} = \begin{cases}1/d & \text{if } \, \text{dist}(\mathbf{x}, \mathbf{y}) = 1, \\ 0 & \text{otherwise}\end{cases}$，即 $P$ 的每一行代表从 $\mathbf{x}$ 出发，经过一步随机游走后，落在 $\mathbf{y}$ 的概率。

在继续之前，我们需要把布尔函数视作向量。我们已经知道，布尔函数与真值表是等价的，真值表就可以视作一个向量；同样的，我们也可以用前文的正交基来构成向量。

不妨混用向量和函数的记号，对于函数 $f$，$P f$ 就是一个向量（也是函数）。

例如，对于集合 $B \subseteq \{0, 1\}^d$，定义指示器函数 $\mathbf{1}_B(x) = \begin{cases}1 & \text{if } \, x \in B, \\ 0 & \text{otherwise}\end{cases}$。

那么 $P \mathbf{1}_B(x)$ 表示，起点为 $\mathbf{x}$，经过一步随机游走后，落在 $B$ 中的概率。这是因为

\begin{align}
(P \mathbf{1}_B)_x &= \sum_{y \in \{0, 1\}^d} P_{\mathbf{x}, \mathbf{y}} \mathbf{1}_B(y) \\
&= \sum_{y \in B} P_{\mathbf{x}, \mathbf{y}}.
\end{align}

考虑对于 Walsh 函数 $\chi_S$，$(P \chi_S)_x$ 就表示 $P$ 矩阵第 $\mathbf{x}$ 行与 $\chi_S$ 的内积，即

\[
(P \chi_S)_x = \sum_{y \in \{0, 1\}^d} P_{\mathbf{x}, \mathbf{y}} \chi_S(y)
\]

根据定义，只有当 $\mathbf{x}$ 与 $\mathbf{y}$ 的距离为 $1$ 时，$P_{\mathbf{x}, \mathbf{y}} = 1/d$，所以

\begin{align}
(P \chi_S)_x &= \sum_{y | \text{dist}(\mathbf{x}, \mathbf{y}) = 1} \frac{1}{d} \chi_S(y) \\
&= \frac{1}{d} \sum_{y | \text{dist}(\mathbf{x}, \mathbf{y}) = 1} \chi_S(y) \\
&= \frac{1}{d} \sum_{i \in [d]} \chi_S(\mathbf{x}^{\mathbf{x}_i \leftarrow 1 - \mathbf{x}_i}) \\
&= \frac{1}{d} \left(\sum_{i \in [d] \setminus S} \chi_S(\mathbf{x}^{\mathbf{x}_i \leftarrow 1 - \mathbf{x}_i}) + \sum_{i \in S} \chi_S(\mathbf{x}^{\mathbf{x}_i \leftarrow 1 - \mathbf{x}_i})\right) \\
&= \frac{1}{d} \left(\sum_{i \in [d] \setminus S} \chi_S(\mathbf{x}) + \sum_{i \in S} (-1) \chi_S(\mathbf{x})\right) \\
&= \frac{d - 2|S|}{d} \chi_S(\mathbf{x})
\end{align}

因此，我们有

\[
P \chi_S = \left(1 - \frac{2|S|}{d}\right) \chi_S.
\]

\begin{lemma}[随机游走矩阵的特征值]\label{lem:random-walk-eigenvalues}
随机游走矩阵 $P$ 有特征向量 $\chi_S$，对应的特征值为 $1 - \frac{2|S|}{d}$。
\end{lemma}

从特征值的角度看，引理~\ref{lem:random-walk-eigenvalues}完全刻画了随机游走矩阵的谱性质。

下面，我们开始进入正题。

之前提到的 $P \mathbf{1}_B(x)$ 表示，起点为 $\mathbf{x}$，经过一步随机游走后，落在 $B$ 中的概率；很自然的，$P^r \mathbf{1}_B(x)$ 表示从 $\mathbf{x}$ 出发，经过 $r$ 步随机游走后，落在 $B$ 中的概率。

那么我们考虑概率，

\begin{align}
\Pr[\mathbf{W}_r(\mathbf{u}) \in B \mid \mathbf{u} \in B] &= \frac{\Pr[\mathbf{W}_r(\mathbf{u}) \in B \text{ and } \mathbf{u} \in B]}{\Pr[\mathbf{u} \in B]} \\
&= \frac{2^d}{|B|} \langle P^r \mathbf{1}_B, \mathbf{1}_B \rangle.
\end{align}

在对其应用傅立叶变换后，

\begin{align}
\Pr[\mathbf{W}_r(\mathbf{u}) \in B \mid \mathbf{u} \in B] &= \frac{2^d}{|B|} \langle P^r \mathbf{1}_B, \mathbf{1}_B \rangle \\
&= \frac{2^d}{|B|} \sum_{S \subseteq [d]} \widehat{P^r \mathbf{1}_B}(S) \widehat{\mathbf{1}_B}(S) \\
&= \frac{2^d}{|B|} \sum_{S \subseteq [d]} \widehat{\mathbf{1}_B}(S)^2 \left(1 - \frac{2|S|}{d}\right)^r
\end{align}

由于我们假设，$r$ 是奇数，在 $|S| > d/2$ 时，$(1 - \frac{2|S|}{d})^r$ 会变为负数，这恰恰是我们需要的。舍弃负项，有

\begin{align}
\Pr[\mathbf{W}_r(\mathbf{u}) \in B \mid \mathbf{u} \in B] &= \frac{2^d}{|B|} \sum_{S \subseteq [d]} \widehat{\mathbf{1}_B}(S)^2 \left(1 - \frac{2|S|}{d}\right)^r \\
&\leq \frac{2^d}{|B|} \sum_{S \subseteq [d], |S| \leq d/2} \widehat{\mathbf{1}_B}(S)^2 \left(1 - \frac{2|S|}{d}\right)^r.
\end{align}

现在开始应用定理~\ref{thm:hypercontractivity}中的$(p, 2)$-超收缩性定理。

我们已知

\[
\|T_\rho f\|_2 \leq \|f\|_{1 + \rho^2},
\]

两边平方，得到

\[
\|T_\rho f\|_2^2 \leq \|f\|_{1 + \rho^2}^2.
\]

而对于 $(T_\rho f)^2$ 的傅立叶变换，利用定理~\ref{thm:plancherel}（普朗歇尔定理），我们有

\[
\|T_\rho f\|_2^2 = \langle T_\rho f, T_\rho f \rangle = \sum_{S \subseteq [d]} \widehat{T_\rho f}(S)^2 = \sum_{S \subseteq [d]} \rho^{2|S|} \widehat{f}(S)^2.
\]

而对于另一端的 $\|f\|_{1 + \rho^2}^2$，我们有

\[
\|f\|_{1 + \rho^2}^2 = \mathbb{E}[f(\mathbf{x})^{1 + \rho^2}]^{2/(1 + \rho^2)} = \left(\frac{1}{2^d} \sum_{\mathbf{x} \in \{0, 1\}^d} f(\mathbf{x})^{1 + \rho^2}\right)^{2/(1 + \rho^2)}.
\]

拼起来，我们有

\[
\sum_{S \subseteq [d]} \rho^{2|S|} \widehat{f}(S)^2 \leq \left(\frac{1}{2^d} \sum_{\mathbf{x} \in \{0, 1\}^d} f(\mathbf{x})^{1 + \rho^2}\right)^{2/(1 + \rho^2)}.
\]

将 $f$ 替换为 $\mathbf{1}_B$，得到

\[
\sum_{S \subseteq [d]} \rho^{2|S|} \widehat{\mathbf{1}_B}(S)^2 \leq \left(\frac{1}{2^d} \sum_{\mathbf{x} \in \{0, 1\}^d} \mathbf{1}_B(\mathbf{x})^{1 + \rho^2}\right)^{2/(1 + \rho^2)} = \left(\frac{1}{2^d} |B|\right)^{2/(1 + \rho^2)}.
\]

回到之前的式子

\begin{align}
\Pr[\mathbf{W}_r(\mathbf{u}) \in B \mid \mathbf{u} \in B] &\leq \frac{2^d}{|B|} \sum_{S \subseteq [d], |S| \leq d/2} \widehat{\mathbf{1}_B}(S)^2 \left(1 - \frac{2|S|}{d}\right)^r \\
&\leq \frac{2^d}{|B|} \sum_{S \subseteq [d], |S| \leq d/2} \widehat{\mathbf{1}_B}(S)^2 \left(1 - \frac{2|S|}{d}\right)^r e^{-2r|S|/d}
\end{align}

现在取 $\rho = e^{-r/d}$，则

\begin{align}
\Pr[\mathbf{W}_r(\mathbf{u}) \in B \mid \mathbf{u} \in B] &\leq \frac{2^d}{|B|} \sum_{S \subseteq [d], |S| \leq d/2} \widehat{\mathbf{1}_B}(S)^2 \left(1 - \frac{2|S|}{d}\right)^r e^{-2r|S|/d} \\
&\leq \frac{2^d}{|B|} \left(\frac{|B|}{2^d}\right)^{2/(1 + e^{-2r/d})} \\
&= \left(\frac{|B|}{2^d}\right)^{\frac{e^{2r/d} - 1}{e^{2r/d} + 1}}.
\end{align}

